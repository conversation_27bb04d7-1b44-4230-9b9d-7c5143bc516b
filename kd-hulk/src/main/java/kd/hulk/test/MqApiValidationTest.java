package kd.hulk.test;

import kd.hulk.api.MqApi;
import kd.hulk.vo.RbReportStatusPostBackVo;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * MqApi Validation Test Class
 */
public class MqApiValidationTest {

    public static void main(String[] args) {
        MqApiValidationTest test = new MqApiValidationTest();

        System.out.println("Starting Excel data validation tests...");

        // Test 1: Valid data
        test.testValidData();

        // Test 2: Missing required fields
        test.testMissingRequiredFields();

        System.out.println("All tests completed!");
    }

    /**
     * Test valid data
     */
    public void testValidData() {
        System.out.println("\n=== Test 1: Valid data ===");
        try {
            InputStream inputStream = createTestExcel(
                "ORDER001", "REPORT001", "ZhangSan", "13812345678",
                "2023-12-01 10:00:00", "2023-12-01 09:30:00",
                "Beijing Chaoyang District", 1, "Driver Insurance", "0", "1",
                "2023-12-02 10:00:00", "1", "2023-12-02 11:00:00"
            );

            MqApi mqApi = new MqApi();
            List<RbReportStatusPostBackVo> result = new ArrayList<>();
            mqApi.readExcel(inputStream, result);

            System.out.println("✓ Valid data test passed, read " + result.size() + " records");
        } catch (Exception e) {
            System.out.println("✗ Valid data test failed: " + e.getMessage());
        }
    }

    /**
     * Test missing required fields
     */
    public void testMissingRequiredFields() {
        System.out.println("\n=== Test 2: Missing required fields ===");
        try {
            InputStream inputStream = createTestExcel(
                "", "REPORT001", "", "13812345678",
                "", "2023-12-01 09:30:00",
                "", null, "", "", "",
                "2023-12-02 10:00:00", "1", "2023-12-02 11:00:00"
            );

            MqApi mqApi = new MqApi();
            List<RbReportStatusPostBackVo> result = new ArrayList<>();
            mqApi.readExcel(inputStream, result);

            System.out.println("✗ Should throw validation exception but didn't");
        } catch (RuntimeException e) {
            if (e.getMessage().contains("Excel data validation failed") || e.getMessage().contains("不能为空")) {
                System.out.println("✓ Required fields validation correct, error message:");
                System.out.println(e.getMessage());
            } else {
                System.out.println("✗ Incorrect exception message: " + e.getMessage());
            }
        } catch (Exception e) {
            System.out.println("✗ Unexpected exception: " + e.getMessage());
        }
    }



    /**
     * Create test Excel file
     */
    private InputStream createTestExcel(String orderNo, String reportNo, String driverName, String driverPhone,
                                      String reportTime, String accidentTime, String address, Integer caseTimes,
                                      String insuranceType, String reportType, String reportStatus,
                                      String endCaseTime, String compensationStatus, String compensationTime) {
        try {
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("TestData");

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "订单号", "报案号", "司机姓名", "司机手机号", "报案时间", "事故时间", "事故详细地址", "赔付次数", "险种名称",
                "报案类型", "案件状态", "结案时间", "赔付状态", "赔付时间", "赔付金额 (分)", "币种", "勘探员", "勘探员手机号", "当前未决金额(分)"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
            }

            // Create data row
            Row dataRow = sheet.createRow(1);
            dataRow.createCell(0).setCellValue(orderNo != null ? orderNo : "");
            dataRow.createCell(1).setCellValue(reportNo != null ? reportNo : "");
            dataRow.createCell(2).setCellValue(driverName != null ? driverName : "");
            dataRow.createCell(3).setCellValue(driverPhone != null ? driverPhone : "");
            dataRow.createCell(4).setCellValue(reportTime != null ? reportTime : "");
            dataRow.createCell(5).setCellValue(accidentTime != null ? accidentTime : "");
            dataRow.createCell(6).setCellValue(address != null ? address : "");
            if (caseTimes != null) {
                dataRow.createCell(7).setCellValue(caseTimes);
            }
            dataRow.createCell(8).setCellValue(insuranceType != null ? insuranceType : "");
            dataRow.createCell(9).setCellValue(reportType != null ? reportType : "");
            dataRow.createCell(10).setCellValue(reportStatus != null ? reportStatus : "");
            dataRow.createCell(11).setCellValue(endCaseTime != null ? endCaseTime : "");
            dataRow.createCell(12).setCellValue(compensationStatus != null ? compensationStatus : "");
            dataRow.createCell(13).setCellValue(compensationTime != null ? compensationTime : "");
            dataRow.createCell(14).setCellValue(10000); // Amount
            dataRow.createCell(15).setCellValue("CNY"); // Currency
            dataRow.createCell(16).setCellValue("Prospector"); // Prospector
            dataRow.createCell(17).setCellValue("13900000000"); // Prospector phone
            dataRow.createCell(18).setCellValue(5000); // Pending amount

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();

            return new ByteArrayInputStream(outputStream.toByteArray());
        } catch (Exception e) {
            throw new RuntimeException("Failed to create test Excel", e);
        }
    }
}
