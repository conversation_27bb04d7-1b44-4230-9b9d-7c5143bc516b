package kd.hulk.api;

import com.alibaba.fastjson.JSONObject;
import kd.common.context.RedisCommonKeyEnum;
import kd.common.tool.DateUtils;
import kd.common.tool.JsonTool;
import kd.common.tool.RedisTool3;
import kd.common.tool.SFTPTool;
import kd.entity.ClaimCase;
import kd.hulk.service.ClaimCaseService;
import kd.hulk.vo.RbReportStatusPostBackVo;
import kd.main.common.QueueName;
import kd.main.support.email.RbEmailNoticesSup;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

@RestController("/api/mqApi")
@RequestMapping(value = "/api/mqApi")
public class MqApi {
    private Logger logger = LoggerFactory.getLogger(MqApi.class.getName());

    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private ClaimCaseService claimCaseService;

    @PostMapping(value = "exportBeginReport")
    public void exportBeginReportApi(String claimCaseId) {
        if (StringUtils.isEmpty(claimCaseId)){
            throw new RuntimeException("请传输claimCaseId");
        }
        logger.info(">>>>>>>>>>>>>接收到的数据claimCaseId>>>>>成功，json：{}", claimCaseId);
        amqpTemplate.convertAndSend(QueueName.KD_RB_EXPORT_MATERIAL_BEGIN_REPORT, claimCaseId);
    }

    @PostMapping(value = "readReportStatus")
    public void readReportStatusMQ(String date) {
        if (StringUtils.isEmpty(date)) {
            throw new RuntimeException("请传输日期");
//            date = DateUtils.format(new Date(), DateUtils.FORMAT_DATE_YYYY_MM_DD);
        }
        logger.info(">>>>>>>>>>>>>接收到的数据date>>>>>成功，json：{}", date);
        amqpTemplate.convertAndSend(QueueName.KD_RB_READ_REPORT_STATUS, date);
    }


    /**
     * 生成报案数据到excel
     *
     * @param sheet
     * @param claimCaseId
     * @return
     */
    public String exportBeginReport(Sheet sheet, String claimCaseId) {
        logger.info("-----------进入生成报案数据EXCEL方法,claimCaseId:{},时间:{}",claimCaseId,new Date());

        ClaimCase claimCase = claimCaseService.selectByPrimaryKey(claimCaseId);   // TODO 可能需要延迟 并且判空
//        if (claimCase == null) {
//            try {
//                Thread.sleep(1000);
//                claimCase = claimCaseService.selectByPrimaryKey(claimCaseId);
//            }catch (Exception e){
//
//            }
//        }
        int rowNum = 1;

        Map<String, Integer> code2Col = new HashMap<>();
        for (int ii = 0; ii < sheet.getRow(1).getLastCellNum(); ii++) {
            Cell cell = sheet.getRow(1).getCell(ii);
            if (cell != null) {
                String data = cell.getStringCellValue();
                code2Col.put(data, ii);
            }
        }
        excelInsertRow(sheet, rowNum == 1 ? 1 : rowNum - 1, 1 + 1);
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("OrderNo", claimCase.getCustomerPolicyNo());
        dataMap.put("ClaimCaseNo", claimCase.getClaimCaseNo());
        dataMap.put("ReporterName", claimCase.getApplyName());
        dataMap.put("ReporterTel", claimCase.getApplyMobile());
        dataMap.put("AccidentDate", claimCase.getTreatDate() != null ? DateUtils.format(claimCase.getTreatDate(), DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS) : "");
        dataMap.put("AccidentProvinceCode", claimCase.getProvince());
        dataMap.put("AccidentCityCode", claimCase.getCity());
        dataMap.put("AccidentDistrictCode", claimCase.getDistrict());
        dataMap.put("AccidentPlace", claimCase.getAddress());
        dataMap.put("AccidentProcess", claimCase.getDescription());

        // 从拓展字段中取出代驾相关字段
        String exInfo = claimCase.getExInfo();

        JSONObject exInfoJson = JSONObject.parseObject(exInfo);

        JSONObject reportExtendParams = JSONObject.parseObject(exInfoJson.get("reportExtendParams").toString());
        String driverName = StringUtils.isNotEmpty(reportExtendParams.getString("driverName")) ? reportExtendParams.getString("driverName") : "";
        String driverIdNo = StringUtils.isNotEmpty(reportExtendParams.getString("driverIdNo")) ? reportExtendParams.getString("driverIdNo") : "";
        String driverPhone = StringUtils.isNotEmpty(reportExtendParams.getString("driverPhone")) ? reportExtendParams.getString("driverPhone") : "";
        String accidentType = StringUtils.isNotEmpty(reportExtendParams.getString("accidentType")) ? reportExtendParams.getString("accidentType") : "";

        dataMap.put("driverName", driverName);
        dataMap.put("driverIdNo", driverIdNo);
        dataMap.put("driverPhone", driverPhone);
        dataMap.put("accidentType", accidentType);

        JSONObject trackingOrder = JSONObject.parseObject(exInfoJson.get("trackingOrder").toString());
        Long accepteTime = trackingOrder.getLong("accepteTime");
        Long startTime = trackingOrder.getLong("startTime");
        Long endTime = trackingOrder.getLong("endTime");
        Long finishTime = trackingOrder.getLong("finishTime");
        String address = StringUtils.isNotEmpty(trackingOrder.getString("address")) ? trackingOrder.getString("address") : "";

        dataMap.put("accepteTime", accepteTime != null ? DateUtils.format(new Date(accepteTime), DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS) : "");
        dataMap.put("startTime", startTime != null ? DateUtils.format(new Date(startTime), DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS) : "");
        dataMap.put("endTime", endTime != null ? DateUtils.format(new Date(endTime), DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS) : "");
        dataMap.put("finishTime", finishTime != null ? DateUtils.format(new Date(finishTime), DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS) : "");
        dataMap.put("address", StringUtils.isNotBlank(address) ? address : "");
        Row row = sheet.getRow(rowNum++);

        for (String code : code2Col.keySet()) {
            if (dataMap.containsKey(code)) {
                row.getCell(code2Col.get(code)).setCellValue(dataMap.get(code));
            } else {
                row.getCell(code2Col.get(code)).setCellValue("");
            }
        }
        sheet.removeRow(sheet.getRow(rowNum));

        return claimCase.getClaimCaseNo();
    }


    public void exportOffReport(Sheet sheet, String claimCaseNo) {
        int rowNum = 1;

        Map<String, Integer> code2Col = new HashMap<>();
        for (int ii = 0; ii < sheet.getRow(1).getLastCellNum(); ii++) {
            Cell cell = sheet.getRow(1).getCell(ii);
            if (cell != null) {
                String data = cell.getStringCellValue();
                code2Col.put(data, ii);
            }
        }
        excelInsertRow(sheet, rowNum == 1 ? 1 : rowNum - 1, 1 + 1);
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("claimCaseNo", claimCaseNo);
        Row row = sheet.getRow(rowNum++);
        for (String code : code2Col.keySet()) {
            if (dataMap.containsKey(code)) {
                row.getCell(code2Col.get(code)).setCellValue(dataMap.get(code));
            } else {
                row.getCell(code2Col.get(code)).setCellValue("");
            }
        }
        sheet.removeRow(sheet.getRow(rowNum));
    }

    // 读取案件状态
    public void readReportStatus(String date) {
        if (StringUtils.isEmpty(date) || !isValidDate(date)) {
            date = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        logger.info(">>>>>>>>>>>>开始读取,日期:{}<<<<<<<<<<<<<<<<<<", date);

        //人保SFTPTool
        String host = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "RB_SFTP_CONFIG", "host");
        String port = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "RB_SFTP_CONFIG", "port");
        String userName = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "RB_SFTP_CONFIG", "userName");
        String password = RedisTool3.hget(RedisCommonKeyEnum.REDIS_KEY_DICT_GROUP_PREFIX.toString() + "RB_SFTP_CONFIG", "password");


        SFTPTool sftpTool = null;

        try {
            sftpTool = new SFTPTool(host, port, userName, password);
            logger.info("创建人保Sftp连接");

            String basePath = "/RB/ReturnClaimProcessBill/%s";
            String remoteDirectory = String.format(basePath, date); //路径

            logger.info("要获取的路径{}", remoteDirectory);

            // 案件状态回传
            List<String> excelList = sftpTool.listFiles(remoteDirectory, ".xlsx");
            List<RbReportStatusPostBackVo> reportStatusPostBackList = new ArrayList<>();
            for (String fileName : excelList) {
                try {
                    // 获得文件内容的 输入流
                    InputStream inputStream = sftpTool.downloadIs(remoteDirectory, fileName);
                    try {
                        // 读取Excel文件
                        readExcel(inputStream, reportStatusPostBackList);
                    } catch (Exception e) {
                        e.printStackTrace();
                        throw new RuntimeException(e);
                    } finally {
                        if (inputStream != null) {
                            inputStream.close();
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("文件读取失败，文件名为：{}", fileName);
                    // 案件状态读取失败,发送邮件给联络人
                    String content = "人保案件状态处理失败:SFTP目录:" + remoteDirectory + "/" + fileName + "错误原因:" + e.getMessage();

                    logger.error("文件读取失败，发送错误消息给可东人员{}", content);

                    amqpTemplate.convertAndSend(QueueName.KD_CUISINE_POLICY_EMAIL, new RbEmailNoticesSup(content));
                } finally {
                    //断开连接
                    try {
                        if (sftpTool != null) {
                            sftpTool.closeChannel();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    logger.info("断开Sftp连接");
                }
            }


            if (CollectionUtils.isNotEmpty(reportStatusPostBackList)) {
                for (RbReportStatusPostBackVo rbReportStatusPostBackVo : reportStatusPostBackList) {
                    if (StringUtils.isNotEmpty(rbReportStatusPostBackVo.getReportNo())) {
                        Integer auditStatus = StringUtils.isEmpty(rbReportStatusPostBackVo.getReportStatus()) ? null : Integer.valueOf(rbReportStatusPostBackVo.getReportStatus());
                        if (auditStatus == null) {

                        } else {
                            ClaimCase claimCase = claimCaseService.selectByCaseNoAndInsCode(rbReportStatusPostBackVo.getReportNo(), "RB");
                            if (claimCase != null) {
                                claimCase.setApplyName(rbReportStatusPostBackVo.getDriverName());
                                claimCase.setApplyMobile(rbReportStatusPostBackVo.getDriverPhone());
                                claimCase.setCreateTime(DateUtils.parse(rbReportStatusPostBackVo.getReportTime(), DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS));
                                claimCase.setTreatDate(DateUtils.parse(rbReportStatusPostBackVo.getAccidentTime(), DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS));
                                claimCase.setAddress(rbReportStatusPostBackVo.getAccidentDetailAddress());
//                                claimCase.setComeFrom(rbReportStatusPostBackVo.getReportType());   报案类型,不修改

                                // TODO 非空校验
                                claimCase.setStatus(switchStatusByCompensationStatus(rbReportStatusPostBackVo.getCompensationStatus()));
                                if (auditStatus == 1) {
                                    if (rbReportStatusPostBackVo.getCompensationStatus().equals("5")) {
                                        claimCase.setCancelDate(DateUtils.parse(rbReportStatusPostBackVo.getEndCaseTime(), DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS));
                                    } else {
                                        claimCase.setEndDate(DateUtils.parse(rbReportStatusPostBackVo.getEndCaseTime(), DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS));
                                    }
                                } else if (auditStatus == 2) {
                                    claimCase.setEndDate(DateUtils.parse(rbReportStatusPostBackVo.getEndCaseTime(), DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS));
                                }
                                // 空指针异常
                                claimCase.setPayDate(DateUtils.parse(rbReportStatusPostBackVo.getCompensationTime(), DateUtils.FORMAT_DATE_YYYY_MM_DD_HH_MM_SS));

                                // EXCEL中以分为单位,数据库是以元为单位,所以需要转换
                                claimCase.setPayAmount(rbReportStatusPostBackVo.getCompensationAmount() != null 
                                    ? rbReportStatusPostBackVo.getCompensationAmount().divide(new BigDecimal(100)) 
                                    : null);

                                claimCaseService.updateByPrimaryKeySelective(claimCase);

                                String reqData = JsonTool.genByFastJson(claimCase);
                                JSONObject reqJson = JSONObject.parseObject(reqData);
                                reqJson.put("auditStatus", auditStatus);
                                if (auditStatus == 0 ){
                                    reqJson.put("pretrialAmount", rbReportStatusPostBackVo.getPretrialAmount() != null
                                            ? rbReportStatusPostBackVo.getPretrialAmount().divide(new BigDecimal(100))
                                            : null);
                                }
                                // 延迟10秒回退高德
                                amqpTemplate.convertAndSend(QueueName.KD_RB_SCALLION_STATUS_BACK_TTL_EXCHANGE, QueueName.KD_RB_SCALLION_STATUS_BACK_TTL, JsonTool.genByFastJson(reqJson), message -> {
                                    message.getMessageProperties().setExpiration(String.valueOf(10L * 1000L));
                                    return message;
                                });
                            }
                        }
                    }
                }


            }


        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            //断开连接
            try {
                if (sftpTool != null) {
                    sftpTool.closeChannel();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            logger.info("断开Sftp连接");
        }


    }

    // 判断字符传是否为时间格式
    public static boolean isValidDate(String str) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            LocalDate.parse(str, formatter); // 尝试解析字符串
            return true; // 解析成功
        } catch (DateTimeParseException e) {
            return false; // 解析失败，说明格式不匹配
        }
    }

    /// /赔付状态 1-赔付 2-零结 3-商业险拒赔 4-整案拒赔 5-注销
    private String switchStatusByCompensationStatus(String compensationStatus) {
        switch (compensationStatus) {
            case "1":
                return "aex20";       // 赔付
            case "2":
                return "aex22";       // 案件零结
            case "3":
                return "aex24";      // 商业险拒赔
            case "4":
                return "aex21";      // 整案拒赔
            case "5":
                return "aax-1"; // 注销
            default:
                return null;
        }
    }


    public void readExcel(InputStream inputStream, List<RbReportStatusPostBackVo> offlineReportVoList) {
        try (Workbook workbook = new XSSFWorkbook(inputStream)) {
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 跳过表头
            if (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                String[] expectedHeaders = {
                    "订单号", "报案号", "司机姓名", "司机手机号", "报案时间", "事故时间", "事故详细地址", "赔付次数", "险种名称",
                    "报案类型", "案件状态", "结案时间", "赔付状态", "赔付时间", "赔付金额 (分)", "币种", "勘探员", "勘探员手机号", "当前未决金额(分)"
                };
                StringBuilder errorInfo = new StringBuilder();
                for (int colIndex = 0; colIndex < expectedHeaders.length; colIndex++) {
                    String actual = getStringCellValue(row, colIndex);
                    if (!expectedHeaders[colIndex].equals(actual)) {
                        // 列号转字母（A、B、C...）
                        char colChar = (char)('A' + colIndex);
                        errorInfo.append("第").append(colChar).append("列表头错误，应为“")
                            .append(expectedHeaders[colIndex]).append("”，实际为“")
                            .append(actual).append("”；");
                    }
                }
                if (errorInfo.length() > 0) {
                    logger.error("表头校验失败: {}", errorInfo.toString());
                    // 可以选择抛出异常
                     throw new RuntimeException("表头校验失败: " + errorInfo.toString());
                }
            }
            // 逐行处理数据
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                RbReportStatusPostBackVo offlineReportVo = new RbReportStatusPostBackVo();
                int colIndex = 0;
                // 1. 订单号
                offlineReportVo.setInsuranceNo(getStringCellValue(row, colIndex++));

                // 2. 报案号
                offlineReportVo.setReportNo(getStringCellValue(row, colIndex++));

                // 3. 司机姓名
                offlineReportVo.setDriverName(getStringCellValue(row, colIndex++));

                // 4. 司机手机号
                offlineReportVo.setDriverPhone(getStringCellValue(row, colIndex++));

                // 5. 报案时间
                offlineReportVo.setReportTime(parseDateCell(row, colIndex++, sdf));

                // 6. 事故时间
                offlineReportVo.setAccidentTime(parseDateCell(row, colIndex++, sdf));

                // 7. 事故详细地址
                offlineReportVo.setAccidentDetailAddress(getStringCellValue(row, colIndex++));

                // 8. 事故经度
//                offlineReportVo.setAccidentLongitude(getNumericCellValue(row, colIndex++));

                // 9. 事故维度
//                offlineReportVo.setAccidentDimension(getNumericCellValue(row, colIndex++));

                // 10. 赔付次数
                offlineReportVo.setCaseTimes(getIntegerCellValue(row, colIndex++));

                // 11. 险种名称
                offlineReportVo.setInsuranceTypeName(getStringCellValue(row, colIndex++));

                // 12. 报案类型：需要转换中文标签为code
                String reportTypeInput = getStringCellValue(row, colIndex++);
                offlineReportVo.setReportType(convertReportType(reportTypeInput));

                // 13. 案件状态：需要转换中文标签为code
                String reportStatusInput = getStringCellValue(row, colIndex++);
                offlineReportVo.setReportStatus(convertReportStatus(reportStatusInput));

                // 14. 结案时间
                offlineReportVo.setEndCaseTime(parseDateCell(row, colIndex++, sdf));

                // 15. 赔付状态：需要转换中文标签为code
                String compensationStatusInput = getStringCellValue(row, colIndex++);
                offlineReportVo.setCompensationStatus(convertCompensationStatus(compensationStatusInput));

                // 16. 赔付时间
                offlineReportVo.setCompensationTime(parseDateCell(row, colIndex++, sdf));

                // 17. 赔付金额
                offlineReportVo.setCompensationAmount(new BigDecimal(getNumericCellValue(row, colIndex++)));

                // 18. 币种
                offlineReportVo.setCurrency(getStringCellValue(row, colIndex++));

                // 19. 勘探员
                offlineReportVo.setProspector(getStringCellValue(row, colIndex++));

                // 20. 勘探员手机号
                offlineReportVo.setProspectorPhone(getStringCellValue(row, colIndex++));

                // 21. 当前未决金额
                offlineReportVo.setPretrialAmount(new BigDecimal(getNumericCellValue(row, colIndex)));

                offlineReportVoList.add(offlineReportVo);
            }
        } catch (Exception e) {
            logger.error("读取Excel文件失败：{}", e.getMessage(), e);
            throw new RuntimeException("表头校验失败: " + e.getMessage());
        }
    }

// === 状态字段转换方法 ===

    /**
     * 转换报案类型
     *
     * @param input 输入值（可能是中文标签或code）
     * @return 对应的code值（默认返回原值）
     */
    private String convertReportType(String input) {
        if (input == null) return null;

        switch (input.trim()) {
            case "线上":
            case "0":
                return "0";
            case "线下":
            case "1":
                return "1";
            default:
                logger.warn("未知的报案类型: {}", input);
                return input; // 保留原值
        }
    }

    /**
     * 转换案件状态
     *
     * @param input 输入值（可能是中文标签或code）
     * @return 对应的code值（默认返回原值）
     */
    private String convertReportStatus(String input) {
        if (input == null) return null;

        switch (input.trim()) {
            case "已报案":
            case "0":
                return "0";
            case "已结案":
            case "1":
                return "1";
            case "已赔付":
            case "2":
                return "2";
            default:
                logger.warn("未知的案件状态: {}", input);
                return input; // 保留原值
        }
    }

    /**
     * 转换赔付状态
     *
     * @param input 输入值（可能是中文标签或code）
     * @return 对应的code值（默认返回原值）
     */
    private String convertCompensationStatus(String input) {
        if (input == null) return null;

        switch (input.trim()) {
            case "赔付":
            case "1":
                return "1";
            case "零结":
            case "2":
                return "2";
            case "商业险拒赔":
            case "3":
                return "3";
            case "整案拒赔":
            case "4":
                return "4";
            case "注销":
            case "5":
                return "5";
            default:
                logger.warn("未知的赔付状态: {}", input);
                return input; // 保留原值
        }
    }

    /**
     * 获取字符串类型的单元格值（处理空值）
     *
     * @param row      当前行
     * @param colIndex 列索引
     * @return 字符串值（空单元格返回null）
     */
    private String getStringCellValue(Row row, int colIndex) {
        Cell cell = row.getCell(colIndex, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_STRING:
                return cell.getStringCellValue().trim();
            case Cell.CELL_TYPE_NUMERIC:
                // 数值类型转为整数格式字符串
                return String.valueOf((int) cell.getNumericCellValue());
            case Cell.CELL_TYPE_BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case Cell.CELL_TYPE_FORMULA:
                // 公式单元格尝试获取字符串值
                try {
                    return cell.getStringCellValue().trim();
                } catch (IllegalStateException e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return null;
        }
    }

    /**
     * 获取数值类型的单元格值（处理空值）
     *
     * @param row      当前行
     * @param colIndex 列索引
     * @return 双精度浮点数值（空单元格返回0.0）
     */
    private double getNumericCellValue(Row row, int colIndex) {
        Cell cell = row.getCell(colIndex, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
        if (cell == null) return 0.0;

        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_NUMERIC:
                return cell.getNumericCellValue();
            case Cell.CELL_TYPE_STRING:
                try {
                    // 处理带逗号的数字字符串（如"1,000.50"）
                    String numStr = cell.getStringCellValue().replaceAll(",", "");
                    return Double.parseDouble(numStr.trim());
                } catch (NumberFormatException e) {
                    return 0.0;
                }
            default:
                return 0.0;
        }
    }

    /**
     * 获取整数类型的单元格值（处理空值）
     *
     * @param row      当前行
     * @param colIndex 列索引
     * @return 整数值（空单元格返回null）
     */
    private Integer getIntegerCellValue(Row row, int colIndex) {
        Cell cell = row.getCell(colIndex, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_NUMERIC:
                return (int) cell.getNumericCellValue();
            case Cell.CELL_TYPE_STRING:
                try {
                    return Integer.parseInt(cell.getStringCellValue().trim());
                } catch (NumberFormatException e) {
                    return null;
                }
            default:
                return null;
        }
    }

    /**
     * 解析日期类型单元格（处理空值）
     *
     * @param row      当前行
     * @param colIndex 列索引
     * @param sdf      日期格式化器
     * @return 格式化后的日期字符串（空单元格返回null）
     */
    private String parseDateCell(Row row, int colIndex, SimpleDateFormat sdf) {
        Cell cell = row.getCell(colIndex, Row.MissingCellPolicy.RETURN_BLANK_AS_NULL);
        if (cell == null) return null;

        try {
            switch (cell.getCellType()) {
                case Cell.CELL_TYPE_NUMERIC:
                    // 处理Excel日期格式
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return sdf.format(cell.getDateCellValue());
                    } else {
                        // 数值型日期（如20230715）
                        return String.valueOf((long) cell.getNumericCellValue());
                    }
                case Cell.CELL_TYPE_STRING:
                    // 字符串型日期
                    return cell.getStringCellValue().trim();
                default:
                    return null;
            }
        } catch (Exception e) {
            logger.warn("日期解析失败，列索引：{}", colIndex, e);
            return null;
        }
    }

    /**
     * @param sheet   要操作的sheet
     * @param starRow 在第几行下
     * @param rows    插入几行
     */
    private void excelInsertRow(Sheet sheet, int starRow, int rows) {
        starRow = starRow - 1;
        for (int i = 0; i < rows; i++) {
            Row sourceRow = null;
            Row targetRow = null;
            Cell sourceCell = null;
            Cell targetCell = null;
            short m;
            starRow = starRow + 1;
            sourceRow = sheet.getRow(starRow);
            targetRow = sheet.createRow(starRow + 1);
            targetRow.setHeight(sourceRow.getHeight());
            for (m = sourceRow.getFirstCellNum(); m < sourceRow.getLastCellNum(); m++) {
                sourceCell = sourceRow.getCell(m);
                targetCell = targetRow.createCell(m);
                targetCell.setCellStyle(sourceCell.getCellStyle());
                targetCell.setCellType(sourceCell.getCellType());
            }
        }
    }


}
